<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1600"
    @cancel="cancel"
    modalHeight="880"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <div class="header" v-if="!!dataSource" style="margin-top: -10px">
        <div class="item">
          <div class="label">方案名称:&nbsp;</div>
          <div class="value">
            <a-tooltip>
              <template slot="title">{{ dataSource.caseName }}</template>
              {{ dataSource.caseName }}
            </a-tooltip>
          </div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">应用场景:&nbsp;</div>
          <div class="value">{{ sceneOptions.find(ele => ele.value == dataSource.scene)?.label }}</div>
        </div>
        <!-- <div class="item" style="width: 20%">
          <div class="label">预报范围:&nbsp;</div>
          <div class="value">{{ fcstRangeOptions.find(ele => ele.value == dataSource.fcstRange)?.label }}</div>
        </div> -->
        <div class="item" style="width: 30%">
          <div class="label">仿真时段:&nbsp;</div>
          <div class="value">
            {{ `${dataSource.startTime} - ${dataSource.endTime}` }}
          </div>
        </div>
        <div class="item">
          <div class="label">仿真生成时间:&nbsp;</div>
          <div class="value">{{ dataSource.saveTime || '-' }}</div>
        </div>
        <div class="item">
          <div class="label">方案编号:&nbsp;</div>
          <div class="value">{{ dataSource.caseCode }}</div>
        </div>
        <!-- <div class="item" style="width: 20%">
          <div class="label">调度类型:&nbsp;</div>
          <div class="value">{{ dispatchTypeOptions.find(ele => ele.value == dataSource.dispathType)?.label }}</div>
        </div> -->
        <div class="item" style="width: 20%">
          <div class="label">发起人:&nbsp;</div>
          <div class="value">{{ dataSource.createdUserName }}</div>
        </div>
      </div>

      <div style="flex: 1">
        <ShowModelComponent v-if="!!dataSource.chSimId" :chSimId="dataSource.chSimId" :modelId="dataSource.modelId" />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import ShowModelComponent from '../../ShowModelComponent.vue'
  import moment from 'moment'
  import ResultPanel from '../ResultPanel/index.vue'

  export default {
    name: 'FormDrawer',
    props: ['sceneOptions', 'dispatchTypeOptions', 'fcstRangeOptions'],
    components: { AntModal, ResultPanel, ShowModelComponent },
    data() {
      return {
        moment,
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',
        dataSource: {},
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.dataSource = row
        console.log('row', row)
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .header {
    padding: 8px 16px;
    background: #e8f3ff;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    .item {
      display: flex;
      width: 25%;
      height: 30px;
      line-height: 30px;
      .label {
        color: #4e5969;
        white-space: nowrap;
      }
      .value {
        color: #1d2129;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
