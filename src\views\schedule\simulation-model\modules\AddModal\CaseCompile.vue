<template>
  <div class="case-compile-container">
    <!-- <div class="page-title">仿真方案编制</div> -->

    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 地图区域 -->
        <div class="map-section">
          <div class="map-placeholder">
            <!-- 暂时显示占位文本，后续实现地图功能 -->
            <div class="placeholder-text">地图区域</div>
          </div>
        </div>

        <!-- 折线图区域 -->
        <div class="chart-section">
          <div class="section-title">东干渠</div>
          <div class="chart-container">
            <LineEchart
              :dataSource="chartData.dataSource"
              :custom="chartData.custom"
              height="100%"
            />
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 供需水表格 -->
        <div class="supply-demand-section">
          <div class="section-header">
            <div class="section-title">供需水</div>
            <div class="supply-input">
              <label>实际可供水量：</label>
              <a-input-number
                v-model="supplyDemandData.actualSupply"
                :min="0"
                :step="0.01"
                placeholder="请输入"
                style="width: 150px; margin-left: 8px;"
              />
              <span style="margin-left: 4px;">万m³</span>
            </div>
          </div>
          <div class="table-container">
            <VxeTable
              ref="supplyDemandTableRef"
              size="small"
              :columns="supplyDemandColumns"
              :tableData="supplyDemandData.tableData"
              :tablePage="false"
              :isShowTableHeader="false"
              height="200px"
            />
          </div>
        </div>

        <!-- 下方两个表格 -->
        <div class="bottom-tables">
          <!-- 水位表格 -->
          <div class="water-level-section">
            <div class="section-title">水位</div>
            <div class="table-container">
              <VxeTable
                ref="waterLevelTableRef"
                size="small"
                :columns="waterLevelColumns"
                :tableData="waterLevelData"
                :tablePage="false"
                :isShowTableHeader="false"
                height="200px"
              />
            </div>
          </div>

          <!-- 流量表格 -->
          <div class="flow-section">
            <div class="section-title">流量</div>
            <div class="table-container">
              <VxeTable
                ref="flowTableRef"
                size="small"
                :columns="flowColumns"
                :tableData="flowData"
                :tablePage="false"
                :isShowTableHeader="false"
                height="200px"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import VxeTable from '@/components/VxeTable/index.vue'
  import { LineEchart } from '@/components/Echarts'
  import { getChDeptFlow, forecast, getInWaterPage, getChWaterList, getMaxOutWater } from '../../services'

  export default {
    name: 'CaseCompile',
    props: ['baseInfo', 'projectFlows'],
    components: {
      VxeTable,
      LineEchart
    },
    data() {
      return {
        // 原有数据
        mapIns: null,
        dispatchCaseOptions: {},
        tableKey: 1,
        loading: false,
        allData: [],
        active: undefined,
        tableData: [],
        tableColumns: [],

        // 供需水数据
        supplyDemandData: {
          actualSupply: undefined, // 实际可供水量
          tableData: [] // 需水口数据
        },

        // 水位数据
        waterLevelData: [],

        // 流量数据
        flowData: [],

        // 折线图数据
        chartData: {
          dataSource: [
            {
              name: '水位',
              color: '#1890ff', // 蓝色
              data: [] // 格式: [['时间', 水位值], ['时间', 水位值], ...]
            }
          ],
          custom: {
            shortValue: true,
            dataZoom: false,
            showAreaStyle: false,
            xLabel: '时间',
            yLabel: '水位(m)',
            legend: false,
            grid: {
              left: '10%',
              right: '10%',
              top: '15%',
              bottom: '15%',
              containLabel: true
            }
          }
        }
      }
    },
    computed: {
      // 供需水表格列配置
      supplyDemandColumns() {
        return [
          {
            field: 'waterIntakeName',
            title: '需水口',
            width: '50%',
            align: 'center'
          },
          {
            field: 'demandWater',
            title: '需水量(万m³)',
            width: '50%',
            align: 'center',
            formatter: ({ cellValue }) => {
              return cellValue !== undefined && cellValue !== null ? cellValue.toFixed(2) : '0.00'
            }
          }
        ]
      },

      // 水位表格列配置
      waterLevelColumns() {
        return [
          {
            field: 'dispatchObject',
            title: '调度对象',
            width: '50%',
            align: 'center'
          },
          {
            field: 'waterLevel',
            title: '水位(m)',
            width: '50%',
            align: 'center',
            formatter: ({ cellValue }) => {
              return cellValue !== undefined && cellValue !== null ? cellValue.toFixed(2) : '0.00'
            }
          }
        ]
      },

      // 流量表格列配置
      flowColumns() {
        return [
          {
            field: 'dispatchObject',
            title: '调度对象',
            width: '50%',
            align: 'center'
          },
          {
            field: 'flow',
            title: '流量(m³/s)',
            width: '50%',
            align: 'center',
            formatter: ({ cellValue }) => {
              return cellValue !== undefined && cellValue !== null ? cellValue.toFixed(2) : '0.00'
            }
          }
        ]
      }
    },
    watch: {
      baseInfo: {
        handler(newVal) {
          if (newVal && newVal.startTime && newVal.endTime && newVal.simulateType) {
            this.loadSupplyDemandData()
            this.loadWaterFlowData()
          }
        },
        immediate: true,
        deep: true
      }
    },
    created() {
      getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        //调度方案选项
        this.dispatchCaseOptions = (res.data?.data || []).map(el => ({
          ...el,
          label: el.caseName,
          value: el.inWaterId,
        }))
        console.log('this.dispatchCaseOptions 131', this.dispatchCaseOptions)
      })
    },
    methods: {
      // 加载供需水数据
      async loadSupplyDemandData() {
        if (!this.baseInfo || !this.baseInfo.startTime || !this.baseInfo.endTime) {
          return
        }

        try {
          const params = {
            startTime: this.baseInfo.startTime,
            endTime: this.baseInfo.endTime,
            scene: this.baseInfo.simulateType
          }

          const response = await getChDeptFlow(params)
          console.log('供需水数据响应:', response)

          if (response.data) {
            const { records, supplyWaterValue } = response.data

            console.log('供需水原始数据:', { records, supplyWaterValue })

            // 设置实际可供水量
            this.supplyDemandData.actualSupply = supplyWaterValue

            // 设置需水口数据
            this.supplyDemandData.tableData = (records || []).map(item => ({
              waterIntakeName: item.deptName, // 需水口名称
              demandWater: item.waterDemandValue // 需水量(万m³)
            }))

            console.log('处理后的供需水表格数据:', this.supplyDemandData.tableData)
            console.log('实际可供水量:', this.supplyDemandData.actualSupply)
          }
        } catch (error) {
          console.error('获取供需水数据失败:', error)
          this.$message.error('获取供需水数据失败')
        }
      },

      // 加载水位流量数据
      async loadWaterFlowData() {
        try {
          const response = await getChWaterList()
          console.log('水位流量数据响应:', response)

          if (response.data) {
            const riverData = response.data || []

            // 处理水位和流量数据
            let waterLevelData = []
            let flowData = []
            let chartData = []

            // 遍历所有河道数据
            riverData.forEach(river => {
              if (river.sites && river.sites.length > 0) {
                river.sites.forEach(site => {
                  // 水位数据
                  waterLevelData.push({
                    dispatchObject: site.siteName,
                    waterLevel: site.wlv
                  })

                  // 流量数据 
                  flowData.push({
                    dispatchObject: site.siteName,
                    flow: site.flow
                  })
                })
              }
            })

            // 设置表格数据
            this.waterLevelData = waterLevelData
            this.flowData = flowData

            // 更新折线图数据 - 使用第二个河道的数据（data[1]）
            console.log('河道数据总数:', riverData.length)
            console.log('所有河道数据:', riverData)

            if (riverData.length > 1 && riverData[1].sites) {
              console.log('使用第二个河道数据:', riverData[1])
              console.log('第二个河道站点数据:', riverData[1].sites)

              chartData = riverData[1].sites
                .filter(site => site.mileage !== null && site.mileage !== undefined &&
                               site.wlv !== null && site.wlv !== undefined)
                .map(site => {
                  console.log('站点数据:', site, '里程:', site.mileage, '水位:', site.wlv)
                  return [
                    parseFloat(site.mileage) || 0, // 横坐标：渠道断面（里程）
                    parseFloat(site.wlv) || 0      // 纵坐标：水位
                  ]
                })
                .sort((a, b) => a[0] - b[0]) // 按里程排序

              console.log('处理后的图表数据:', chartData)

              this.chartData = {
                dataSource: [
                  {
                    name: '水位',
                    color: '#1890ff',
                    data: chartData
                  }
                ],
                custom: {
                  shortValue: true,
                  dataZoom: false,
                  showAreaStyle: false,
                  xLabel: '渠道断面(km)',
                  yLabel: '水位(m)',
                  legend: false,
                  grid: {
                    left: '10%',
                    right: '10%',
                    top: '15%',
                    bottom: '15%',
                    containLabel: true
                  }
                }
              }

              console.log('最终图表配置:', this.chartData)
            } else if (riverData.length > 0 && riverData[0].sites) {
              // 如果没有第二个河道，使用第一个河道的数据
              console.log('使用第一个河道数据:', riverData[0])

              chartData = riverData[0].sites
                .filter(site => site.mileage !== null && site.mileage !== undefined &&
                               site.wlv !== null && site.wlv !== undefined)
                .map(site => [
                  parseFloat(site.mileage) || 0,
                  parseFloat(site.wlv) || 0
                ])
                .sort((a, b) => a[0] - b[0]) // 按里程排序

              this.chartData = {
                dataSource: [
                  {
                    name: '水位',
                    color: '#1890ff',
                    data: chartData
                  }
                ],
                custom: {
                  shortValue: true,
                  dataZoom: false,
                  showAreaStyle: false,
                  xLabel: '渠道断面(km)',
                  yLabel: '水位(m)',
                  legend: false,
                  grid: {
                    left: '10%',
                    right: '10%',
                    top: '15%',
                    bottom: '15%',
                    containLabel: true
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error('获取水位流量数据失败:', error)
          this.$message.error('获取水位流量数据失败')
        }
      },

      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.$nextTick(() => {
          this.mapIns.resize()
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        this.mapIns.addControl(this.mapOverlayIns)
        this.dealLayers()
      },
      onTabChange(activeKey) {
        this.tableData = this.allData.find(el => el.projectId === activeKey).res
        this.tableColumns = this.allData.find(el => el.projectId === activeKey).columns
        this.tableKey += 1
      },
      save() {
        // 直接调用保存数据方法，不需要表单验证
        this.saveFormData()
      },
      // 保存数据（原有的逻辑保持不变，用于向父组件传递数据）
      async saveFormData() {
        try {
          // 调度方案 默认第一个
          if (this.dispatchCaseOptions && this.dispatchCaseOptions.length > 0) {
            const inWaterId = this.dispatchCaseOptions[0].inWaterId
            const maxOutWaterRes = await getMaxOutWater({ inWaterId: inWaterId })

            // 获取当前的供需水和水位流量数据
            const params = {
              startTime: this.baseInfo.startTime,
              endTime: this.baseInfo.endTime,
              scene: this.baseInfo.simulateType,
            }

            const chDeptFlowRes = await getChDeptFlow(params)
            const waterFlowRes = await getChWaterList()

            this.formData = {
              ...this.baseInfo,
              scene: this.baseInfo.simulateType,
              inWaterId: inWaterId,
              chDeptFlows: chDeptFlowRes.data,
              chSiteWaters: waterFlowRes.data,
              outWaters: maxOutWaterRes.data,
            }

            console.log('formData', this.formData)
            this.$emit('saveData', this.formData)
          }
        } catch (error) {
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败，请重试')
          this.$emit('saveData', false)
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .case-compile-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .page-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .main-content {
      flex: 1;
      display: flex;
      gap: 20px;
      height: calc(100% - 50px);
    }

    .left-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .right-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      // margin-bottom: 12px;
      color: #333;
    }

    .map-section {
      flex: 2; // 地图区域高度是折线图的2倍
      padding: 16px;
      background: #fafafa;

      .map-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f0f0;
        border-radius: 4px;

        .placeholder-text {
          color: #999;
          font-size: 16px;
        }
      }
    }

    .chart-section {
      flex: 1; // 折线图高度
      padding: 16px;

      .chart-container {
        height: 100%;
        padding: 10px; // 给图表添加内边距，避免坐标轴被遮挡
      }
    }

    .supply-demand-section {
      flex: 1;
      padding: 16px;

      .section-header {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
      }

      .supply-input {
        display: flex;
        align-items: center;

        label {
          font-weight: 500;
          color: #333;
        }
      }

      .table-container {
        height: calc(100% - 60px);
      }
    }

    .bottom-tables {
      flex: 1;
      display: flex;
      gap: 16px;
    }

    .water-level-section,
    .flow-section {
      flex: 1;
      padding: 16px;

      .table-container {
        height: calc(100% - 40px);
      }
    }
  }

  ::v-deep .vxe-table--render-default.size--small .vxe-header--row .vxe-header--column .vxe-cell {
    height: 40px !important;
  }

  ::v-deep .ant-radio-group {
    .ant-radio-wrapper {
      font-size: inherit;
      span {
        &:last-child {
          padding: 0 0 0 5px;
        }
      }
    }
  }

  ::v-deep .ant-empty-image svg {
    width: 100%;
  }
</style>
