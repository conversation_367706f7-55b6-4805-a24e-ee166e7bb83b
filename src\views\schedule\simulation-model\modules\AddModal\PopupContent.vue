<template>
  <div class="container">
    <div class="header">
      <div class="icon">{{ item?.ditch?.projectName.slice(0, 1) }}</div>
      <div class="name">{{ item?.projectName }}</div>
      <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label">{{ indicator.label }}:</div>
      <div class="value">{{ indicator.value }}m3/s</div>
    </div>

    <div style="text-align: center; margin-bottom: 14px">
      <a-button type="primary" size="small" @click.stop="item.onProcessClick(item)">过程曲线</a-button>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PopupContent',
    props: ['item'],
    computed: {
      indicator() {
        // 	有闸有泵(0闸1泵)2单闸3无闸无泵4单泵
        if (this.item.type === 0 || this.item.type === 2) {
          return { label: '过闸流量', value: this.item.outFlow }
        }
        if (this.item.type === 1 || this.item.type === 4) {
          return { label: '抽水流量', value: this.item.inFlow }
        }
      },
    },
    mounted() {},
  }
</script>
<style lang="less">
  .mapboxgl-popup-content {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .container {
    width: 160px;
    max-height: 106px;
    position: relative;
    display: flex;
    flex-direction: column;
    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        margin: 0 13px 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator {
      display: flex;
      padding: 9px 8px;
      .label {
        color: '#4E5969';
      }
      .value {
        color: #1d2129;
      }
    }
  }
</style>
