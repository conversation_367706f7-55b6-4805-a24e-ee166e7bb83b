<template>
  <div style="margin-top: 16px; height: calc(100% - 12px); display: flex; width: 40%; background: #fff">
    <div style="width: 460px; display: flex; flex-direction: column">
      <div
        style="
          position: relative;
          left: 0px;
          top: 0px;
          width: 460px;
          height: 574px;
          border-radius: 8px;
          overflow: hidden;
          background: #f7f8fa;
          box-sizing: border-box;
          border: 1px solid #e5e6eb;
          display: flex;
          flex-direction: column;
        "
      >
        <div class="header">
          <div class="title">推演概化图</div>
          <div class="unit">流量单位：m³/s</div>
        </div>
        <div class="content">
          <!-- v-if="state.projects['430922103000_XSDFSZ']?.isOpen == 1" -->
          <div
            class="left-branch1-top"
            v-if="itemOption.find(item => item.projectCode === '430922103000_XSDFSZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922103000_LTSXHZ']?.isOpen == 1" -->
          <div
            class="left-branch1-center"
            v-if="itemOption.find(item => item.projectCode === '430922103000_LTSXHZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922203000_CJCJZZ']?.isOpen == 1" -->
          <div
            class="left-branch1-bottom"
            v-if="itemOption.find(item => item.projectCode === '430922203000_CJCJZZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- left-branch2 -->
          <!-- v-if="state.projects['430922212000_LJAFSZ']?.isOpen == 1" -->
          <div
            class="left-branch2-top"
            v-if="itemOption.find(item => item.projectCode === '430922212000_LJAFSZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922212000_WJXHZ']?.isOpen == 1" -->
          <div
            class="left-branch2-center"
            v-if="itemOption.find(item => item.projectCode === '430922212000_WJXHZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922203000_GQJZZ']?.isOpen == 1" -->
          <div
            class="left-branch2-bottom"
            v-if="itemOption.find(item => item.projectCode === '430922203000_GQJZZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922203000_ZLYCFSZ']?.isOpen == 1" -->
          <div
            class="left-bottom"
            v-if="itemOption.find(item => item.projectCode === '430922203000_ZLYCFSZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>

          <!-- v-if="state.projects['430922108000_GLCDHX']?.isOpen == 1" -->
          <div class="right-top" v-if="itemOption.find(item => item.projectCode === '430922108000_GLCDHX')?.open > 0">
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922108000_DFLDH']?.isOpen == 1" -->
          <div class="right-middle" v-if="itemOption.find(item => item.projectCode === '430922108000_DFLDH')?.open > 0">
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <!-- v-if="state.projects['430922108000_SSWFSZ']?.isOpen == 1" -->
          <div
            class="right-bottom"
            v-if="itemOption.find(item => item.projectCode === '430922108000_SSWFSZ')?.open > 0"
          >
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>
          <div class="center">
            <img src="@/assets/images/reservior/flow-map-bg-line.png" alt="Dynamic Image" />
          </div>

          <div class="west-branch" :style="{ top: '122px', left: '2px' }">沾溪干渠</div>

          <!-- 小石洞 -->
          <div class="item-title" :style="{ top: '24px', left: '46px' }">
            小石洞
            <!-- {{ state.projects['430922103000_XSDFSZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '2px', left: '42px' }">
            分水闸
            <!-- {{ state.projects['430922103000_XSDFSZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '26px', left: '100px' }">
            <!-- 32 -->
            {{ itemOption.find(item => item.projectCode == '430922103000_XSDFSZ')?.outFlow }}
          </div>
          <!-- 龙头山 -->
          <div class="item-title" :style="{ top: '76px', left: '48px' }">
            龙头山
            <!-- {{ state.projects['430922103000_LTSXHZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '56px', left: '42px' }">
            泄洪闸
            <!-- {{ state.projects['430922103000_LTSXHZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '76px', left: '100px' }">
            <!-- 11 -->
            {{ itemOption.find(item => item.projectCode === '430922103000_LTSXHZ')?.outFlow }}
          </div>
          <!-- 陈家冲 -->
          <div class="item-title" :style="{ top: '143px', left: '57px' }">
            陈家冲
            <!-- {{ state.projects['430922203000_CJCJZZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '164px', left: '52px' }">
            节制闸
            <!-- {{ state.projects['430922203000_CJCJZZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '144px', left: '-4px', textAlign: 'right' }">
            <!-- 22 -->
            {{ itemOption.find(item => item.projectCode === '430922203000_CJCJZZ')?.outFlow }}
          </div>

          <div class="west" :style="{ top: '200px', left: '66px' }">西干渠</div>

          <!-- 李家坳分水闸（西） -->
          <div class="item-title" :style="{ top: '25px', left: '223px' }">
            李家坳
            <!-- {{ state.projects['430922212000_LJAFSZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '2px', left: '220px' }">
            分水闸
            <!-- {{ state.projects['430922212000_LJAFSZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '26px', left: '276px' }">
            <!-- 33 -->
            {{ itemOption.find(item => item.projectCode === '430922212000_LJAFSZ')?.outFlow }}
          </div>
          <!-- 万金泄洪闸  （西） -->
          <div class="item-title" :style="{ top: '87px', left: '228px' }">
            万金
            <!-- {{ state.projects['430922212000_WJXHZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '66px', left: '220px' }">
            泄洪闸
            <!-- {{ state.projects['430922212000_WJXHZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '86px', left: '276px' }">
            <!-- 13 -->
            {{ itemOption.find(item => item.projectCode === '430922212000_WJXHZ')?.outFlow }}
          </div>
          <!-- 高桥节制闸 -->
          <div class="item-title" :style="{ top: '143px', left: '136px' }">
            高桥
            <!-- {{ state.projects['430922203000_GQJZZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '164px', left: '126px' }">
            节制闸
            <!-- {{ state.projects['430922203000_GQJZZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '144px', left: '186px' }">
            <!-- 3 -->
            {{ itemOption.find(item => item.projectCode === '430922203000_GQJZZ')?.outFlow }}
          </div>
          <!-- 子良岩村分水闸 -->
          <div class="item-title" :style="{ top: '225px', left: '134px', width: '100px' }">
            子良岩村
            <!-- {{ state.projects['430922203000_ZLYCFSZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '248px', left: '140px' }">
            分水闸
            <!-- {{ state.projects['430922203000_ZLYCFSZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '225px', left: '72px', textAlign: 'right' }">
            <!-- 2 -->
            {{ itemOption.find(item => item.projectCode === '430922203000_ZLYCFSZ')?.outFlow }}
          </div>
          <!-- 狮山湾分水闸（东） -->
          <div class="item-title" :style="{ top: '180px', left: '250px' }">
            狮山湾
            <!-- {{ state.projects['430922108000_SSWFSZ']?.name }} -->
          </div>
          <div class="item-type" :style="{ top: '156px', left: '244px' }">
            分水闸
            <!-- {{ state.projects['430922108000_SSWFSZ']?.type }} -->
          </div>
          <div class="item-value" :style="{ top: '180px', left: '300px' }">
            <!-- 3.6 -->
            {{ itemOption.find(item => item.projectCode === '430922108000_SSWFSZ')?.outFlow }}
          </div>
          <!-- 大伏岭倒虹（东） -->
          <!-- {{ state.projects['430922108000_DFLDH']?.name }} -->
          <div class="item-title" :style="{ top: '145px', left: '355px' }">大伏岭</div>
          <!-- {{ state.projects['430922108000_DFLDH']?.type }} -->
          <div class="item-type" :style="{ top: '122px', left: '354px', textAlign: 'right' }">倒虹吸</div>
          <!-- {{ state.projects['430922108000_DFLDH']?.flow }} -->
          <div class="item-value" :style="{ top: '170px', left: '353px', textAlign: 'right' }">
            <!-- 21 -->
            {{ itemOption.find(item => item.projectCode === '430922108000_DFLDH')?.outFlow }}
          </div>
          <!-- 郭里冲倒虹吸（东） -->
          <!-- {{ state.projects['430922108000_GLCDHX']?.name }} -->
          <div class="item-title" :style="{ top: '3px', left: '356px' }">郭里冲</div>
          <!-- {{ state.projects['430922108000_GLCDHX']?.type }} -->
          <div class="item-type" :style="{ top: '28px', left: '352px', textAlign: 'right' }">倒虹吸</div>
          <!-- {{ state.projects['430922108000_GLCDHX']?.flow }} -->
          <div class="item-value" :style="{ top: '44px', left: '350px', textAlign: 'right' }">
            <!-- 21 -->
            {{ itemOption.find(item => item.projectCode === '430922108000_GLCDHX')?.outFlow }}
          </div>

          <div class="east" :style="{ top: '200px', left: '354px' }">东干渠</div>

          <div class="source" :style="{ top: '240px', left: '226px' }">总干渠</div>
        </div>
        <div class="footer">
          <div class="map"></div>
          <div class="icon"></div>
          <div class="title"></div>
        </div>
      </div>
      <div style="width: 460px; height: 44px">
        <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" />
      </div>
    </div>
    <div style="width: 460px; margin-left: 12px"></div>
  </div>
</template>

<script lang="jsx">
  import TimePlaySlider from '@/components/TimePlaySlider/index.vue'

  import { getOptions } from '@/api/common'
  import { mapboxPopup } from './popup.js'
  import initLayer from './initLayer.js'

  export default {
    name: 'GeneralizedMap',
    props: ['chSimId', 'dataSource', 'mapData'],
    components: { TimePlaySlider },
    data() {
      return {
        mapIns: null,
        mapOverlayIns: null,
        times: [],
        geojson: null,

        activeProcess: null,
        itemInfo: [],
        itemOption: [],
      }
    },
    computed: {},
    watch: {
      mapData: {
        handler(newVal, oldVal) {
          this.times = [...new Set(newVal[0].resVOS.map(el => el.tm))]
        },
        deep: true,
      },
      itemOption: {
        handler(newVal, oldVal) {},
        deep: true,
      },
    },
    created() {
      getOptions('scaleProjectCode').then(res => {
        this.itemInfo = res.data.map(el => ({
          projectCode: el.key,
          projectName: el.value,
          projectType: el.option1,
        }))
      })
    },
    methods: {
      onTimeChange(time) {
        let arr = []
        this.mapData.forEach(el => {
          arr = arr.concat(el.resVOS.find(ele => ele.tm == time).records)
        })
        const factArr = arr.filter(el => !!+el.longitude && !!+el.latitude)
        this.itemInfo.forEach(el => {
          if (factArr.some(ele => ele.projectCode === el.projectCode)) {
            this.dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
          }
        })
        this.itemOption = [...this.itemInfo]
      },
      dealPopup(curr) {
        let index = this.itemInfo.findIndex(el => el.projectCode === curr.projectCode)
        if (index === -1) {
          this.itemInfo.push({ projectCode: curr.projectCode })
        } else {
          this.itemInfo[index] = { ...this.itemInfo[index], ...curr }
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .header {
    display: flex;
    justify-content: space-between;
    //
    margin: 16px;
    .title {
      // font-weight: bold;
      font-size: 16px;
      font-weight: 500;
      line-height: 28px;
      letter-spacing: 0px;
      align-items: center;
      color: #1d2129;
    }
    .unit {
      display: flex;
      font-size: 12px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      color: #1d2129;
      align-items: center;
    }
  }
  .content {
    height: 280px;
    width: 406px;
    margin: 10px 26px;
    // background: #f5f5f5;
    background: url('~@/assets/images/reservior/flow-map-bg2.png') no-repeat;
    background-size: 100% 100%;
    // display: flex;
    position: relative;

    .left-branch1-top,
    .left-branch1-center,
    .left-branch1-bottom,
    .left-branch2-top,
    .left-branch2-center,
    .left-branch2-bottom,
    .left-bottom,
    .right-top,
    .right-middle,
    .right-bottom,
    .center {
      position: absolute;
      top: 0;
      left: 0;
      img {
        height: 280px;
        width: 406px;
      }
    }
    /* 显示左边 左分支上面部分 */
    .left-branch1-top img {
      margin-top: -7px;
      margin-left: -2px;
      /* 左上  右上 右下 左下*/
      clip-path: polygon(0% 6%, 8% 6%, 8% 12%, 0% 12%);
      // background-size: 100% 100%;
      // background-color: red;
    }

    /* 显示左边 左分支中间部分 */
    .left-branch1-center img {
      margin-left: -2px;
      clip-path: polygon(1% 16%, 14% 16%, 14% 27%, 1% 27%);

      // background-color: red;
    }
    /* 显示左边 左分支下面部分 */
    .left-branch1-bottom img {
      margin-top: 3px;
      margin-left: -2px;
      /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
      clip-path: polygon(1% 34%, 22% 34%, 22% 45%, 1% 45%);
      // background-color: red;
    }
    /* 显示左边 右分支 */
    .left-branch2 img {
      // margin-top: 1px;
      margin-left: -1px;
      /* 左上  右上 右下 左下*/
      clip-path: polygon(28% 6%, 52% 6%, 52% 47%, 28% 47%);
      /* background-color: red; */
    }
    /* 显示左边 右分支上面部分 */
    .left-branch2-top img {
      margin-top: -8px;
      margin-left: -1px;
      /* 左上  右上 右下 左下*/
      clip-path: polygon(46% 6%, 49% 6%, 49% 14%, 46% 14%);
      // background-color: red;
    }

    /* 显示左边 右分支中间部分 */
    .left-branch2-center img {
      margin-left: -1px;
      clip-path: polygon(46% 16%, 51.5% 16%, 51.5% 32%, 46% 32%);
      // background-color: red;
    }
    /* 显示左边 左分支下面部分 */
    .left-branch2-bottom img {
      margin-top: 4px;
      margin-left: -1px;
      /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
      clip-path: polygon(35% 38%, 48.6% 37%, 48.6% 45%, 35% 45%);
      // background-color: red;
    }

    /* 显示左边下面部分 */
    .left-bottom img {
      /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
      margin-top: 8px;
      margin-left: -2px;
      clip-path: polygon(1% 45%, 43% 45%, 44% 78%, 1% 78%);
      // background-color: red;
    }

    /* 显示右部分 */
    .right img {
      // margin-top: 8.5px;
      margin-left: 1px;
      clip-path: polygon(52% 0%, 100% 0%, 100% 78%, 52% 78%);
      background-color: aqua;
    }
    /* 显示右上部分 */
    .right-top img {
      margin-left: 1px;
      clip-path: polygon(50% 0, 100% 0, 100% 12%, 50% 12%);
      // background-color: aqua;
    }

    /* 显示右边中间部分 */
    .right-middle img {
      margin-left: 1px;
      /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
      clip-path: polygon(54% 18%, 100% 18%, 100% 40%, 54% 40%);
      // background-color: red;
    }
    /* 显示右边下面部分 */
    .right-bottom img {
      /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
      margin-top: 8px;
      margin-left: 1px;
      clip-path: polygon(59% 46%, 100% 46%, 100% 78%, 58.8% 78%);
      // background-color: red;
    }
    .center img {
      /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
      margin-top: 12px;
      margin-left: -0.5px;
      clip-path: polygon(46% 73.4%, 60% 73.4%, 60% 98%, 46% 98%);
      /* background-color: red; */
    }

    .west,
    .west-branch,
    .east,
    .source {
      position: absolute;
      width: 80px;
      height: 20px;
      color: #4ae681;
      font-size: 12px;
      font-weight: 500;
    }
    .item-title {
      position: absolute;
      width: 50px;
      height: 20px;
      color: #ffffff;
      font-size: 14px;
      // font-weight: 500
    }
    .item-type {
      position: absolute;
      width: 50px;
      height: 20px;
      color: #b2daea;
      font-size: 12px;
      text-align: center;
    }
    .item-value {
      position: absolute;
      width: 50px;
      height: 20px;
      color: #09c8fc;
      font-size: 14px;
      text-align: left;
    }
  }
  .footer {
    display: flex;
    position: relative;
    .map {
      position: absolute;
      right: 30px;
      top: 0;

      width: 420px;
      height: 120px;
      background: url('~@/assets/images/reservior/reservior-map.png') no-repeat;
      background-size: 100% 100%;
    }
    .icon {
      position: absolute;
      right: 188px;
      top: 16px;
      width: 27px;
      height: 27px;
      background: url('~@/assets/images/reservior/reservior-icon.png') no-repeat;
      background-size: 100% 100%;
    }
    .title {
      position: absolute;
      right: 94px;
      top: 16px;
      width: 92px;
      height: 28px;
      background: url('~@/assets/images/reservior/reservior-title.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
